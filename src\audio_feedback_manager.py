"""
GFLOW-26: Audio Feedback Manager

Provides audio feedback for gesture recognition events, action execution,
and system status changes to enhance user experience.
"""

import os
import threading
import time
from typing import Dict, Any, Optional
from config import AUDIO_FEEDBACK_CONFIG, PROJECT_ROOT

# Try to import audio libraries
try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    print("pygame not available for audio feedback")

try:
    import winsound
    WINSOUND_AVAILABLE = True
except ImportError:
    WINSOUND_AVAILABLE = False


class AudioFeedbackManager:
    """
    Manages audio feedback for gesture recognition system
    
    Features:
    - Multiple audio backend support (pygame, winsound, system)
    - Configurable sound library
    - Volume control
    - Non-blocking audio playback
    - Sound file management
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or AUDIO_FEEDBACK_CONFIG
        
        # Audio settings
        self.enabled = self.config.get('enabled', False)
        self.volume = self.config.get('volume', 0.5)
        self.sound_library = self.config.get('sound_library', 'pygame')
        
        # Sound file paths
        self.sounds_dir = os.path.join(PROJECT_ROOT, 'data', 'sounds')
        self.sounds = self.config.get('sounds', {})
        self.gesture_sounds = self.config.get('gesture_sounds', {})
        self.action_sounds = self.config.get('action_sounds', {})
        
        # Audio backend
        self.audio_backend = None
        self._initialize_audio_backend()
        
        # Create sounds directory
        os.makedirs(self.sounds_dir, exist_ok=True)
        
        # Generate default sounds if they don't exist
        self._ensure_default_sounds()
        
    def _initialize_audio_backend(self):
        """Initialize the audio backend"""
        if not self.enabled:
            return
            
        if self.sound_library == 'pygame' and PYGAME_AVAILABLE:
            try:
                pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
                self.audio_backend = 'pygame'
                print("Audio feedback initialized with pygame")
            except Exception as e:
                print(f"Failed to initialize pygame audio: {e}")
                self._fallback_to_system_audio()
        elif self.sound_library == 'winsound' and WINSOUND_AVAILABLE:
            self.audio_backend = 'winsound'
            print("Audio feedback initialized with winsound")
        else:
            self._fallback_to_system_audio()
            
    def _fallback_to_system_audio(self):
        """Fallback to system audio"""
        if WINSOUND_AVAILABLE:
            self.audio_backend = 'winsound'
            print("Audio feedback using winsound fallback")
        else:
            self.audio_backend = 'system'
            print("Audio feedback using system beeps")
            
    def play_gesture_recognized(self, gesture_type: str = 'static'):
        """Play sound for gesture recognition"""
        if not self.enabled:
            return
            
        # Try specific gesture type sound first
        sound_file = self.gesture_sounds.get(gesture_type)
        if not sound_file:
            sound_file = self.sounds.get('gesture_recognized')
            
        self._play_sound_async(sound_file)
        
    def play_action_executed(self, action_type: str = 'mouse', success: bool = True):
        """Play sound for action execution"""
        if not self.enabled:
            return
            
        if success:
            # Try specific action type sound first
            sound_file = self.action_sounds.get(action_type)
            if not sound_file:
                sound_file = self.sounds.get('action_executed')
        else:
            sound_file = self.sounds.get('action_failed')
            
        self._play_sound_async(sound_file)
        
    def play_recording_start(self):
        """Play sound for recording start"""
        if self.enabled:
            self._play_sound_async(self.sounds.get('recording_start'))
            
    def play_recording_stop(self):
        """Play sound for recording stop"""
        if self.enabled:
            self._play_sound_async(self.sounds.get('recording_stop'))
            
    def play_training_complete(self):
        """Play sound for training completion"""
        if self.enabled:
            self._play_sound_async(self.sounds.get('training_complete'))
            
    def play_error(self):
        """Play sound for errors"""
        if self.enabled:
            self._play_sound_async(self.sounds.get('error'))
            
    def play_cursor_control_on(self):
        """Play sound for cursor control activation"""
        if self.enabled:
            self._play_sound_async(self.sounds.get('cursor_control_on'))
            
    def play_cursor_control_off(self):
        """Play sound for cursor control deactivation"""
        if self.enabled:
            self._play_sound_async(self.sounds.get('cursor_control_off'))
            
    def _play_sound_async(self, sound_file: Optional[str]):
        """Play sound asynchronously"""
        if not sound_file or not self.enabled:
            return
            
        # Start playback in separate thread to avoid blocking
        thread = threading.Thread(target=self._play_sound, args=(sound_file,))
        thread.daemon = True
        thread.start()
        
    def _play_sound(self, sound_file: str):
        """Play sound using configured backend"""
        sound_path = os.path.join(self.sounds_dir, sound_file)
        
        # Check if file exists
        if not os.path.exists(sound_path):
            # Try to generate a default sound
            self._generate_default_sound(sound_file)
            if not os.path.exists(sound_path):
                self._play_system_beep()
                return
                
        try:
            if self.audio_backend == 'pygame':
                self._play_with_pygame(sound_path)
            elif self.audio_backend == 'winsound':
                self._play_with_winsound(sound_path)
            else:
                self._play_system_beep()
        except Exception as e:
            print(f"Error playing sound {sound_file}: {e}")
            self._play_system_beep()
            
    def _play_with_pygame(self, sound_path: str):
        """Play sound using pygame"""
        try:
            sound = pygame.mixer.Sound(sound_path)
            sound.set_volume(self.volume)
            sound.play()
        except Exception as e:
            print(f"pygame playback error: {e}")
            self._play_system_beep()
            
    def _play_with_winsound(self, sound_path: str):
        """Play sound using winsound"""
        try:
            winsound.PlaySound(sound_path, winsound.SND_FILENAME | winsound.SND_ASYNC)
        except Exception as e:
            print(f"winsound playback error: {e}")
            self._play_system_beep()
            
    def _play_system_beep(self):
        """Play system beep as fallback"""
        try:
            if WINSOUND_AVAILABLE:
                winsound.Beep(800, 200)  # 800Hz for 200ms
            else:
                print('\a')  # ASCII bell character
        except Exception:
            pass  # Silent fallback
            
    def _ensure_default_sounds(self):
        """Ensure default sound files exist"""
        # For now, we'll create placeholder files or use system sounds
        # In a full implementation, you would include actual sound files
        
        required_sounds = list(self.sounds.values()) + list(self.gesture_sounds.values()) + list(self.action_sounds.values())
        
        for sound_file in required_sounds:
            if sound_file:
                sound_path = os.path.join(self.sounds_dir, sound_file)
                if not os.path.exists(sound_path):
                    self._generate_default_sound(sound_file)
                    
    def _generate_default_sound(self, sound_file: str):
        """Generate a default sound file"""
        # For this implementation, we'll create simple tone files using pygame
        if not PYGAME_AVAILABLE:
            return
            
        try:
            # Initialize pygame mixer if not already done
            if not pygame.mixer.get_init():
                pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
                
            # Generate different tones for different sound types
            duration = 0.2  # 200ms
            sample_rate = 22050
            
            # Determine frequency based on sound type
            if 'gesture' in sound_file:
                frequency = 800
            elif 'action' in sound_file:
                frequency = 600
            elif 'error' in sound_file:
                frequency = 400
            elif 'recording' in sound_file:
                frequency = 1000
            elif 'training' in sound_file:
                frequency = 1200
            elif 'cursor' in sound_file:
                frequency = 900
            else:
                frequency = 700
                
            # Generate sine wave
            frames = int(duration * sample_rate)
            arr = []
            for i in range(frames):
                wave = 4096 * np.sin(2 * np.pi * frequency * i / sample_rate)
                arr.append([int(wave), int(wave)])  # Stereo
                
            sound = pygame.sndarray.make_sound(np.array(arr))
            
            # Save to file
            sound_path = os.path.join(self.sounds_dir, sound_file)
            pygame.mixer.Sound.play(sound)  # Test play
            
            # Note: pygame doesn't have direct WAV export, so we'll use a placeholder
            # In a real implementation, you'd use a library like scipy.io.wavfile
            with open(sound_path.replace('.wav', '.txt'), 'w') as f:
                f.write(f"Generated sound file for {sound_file}")
                
        except Exception as e:
            print(f"Error generating default sound {sound_file}: {e}")
            
    def set_enabled(self, enabled: bool):
        """Enable or disable audio feedback"""
        self.enabled = enabled
        if enabled and not self.audio_backend:
            self._initialize_audio_backend()
            
    def set_volume(self, volume: float):
        """Set audio volume (0.0 to 1.0)"""
        self.volume = max(0.0, min(1.0, volume))
        
    def set_sound_library(self, library: str):
        """Set sound library (pygame, winsound, system)"""
        if library in ['pygame', 'winsound', 'system']:
            self.sound_library = library
            self._initialize_audio_backend()
            
    def test_audio(self):
        """Test audio system"""
        if not self.enabled:
            print("Audio feedback is disabled")
            return False
            
        print(f"Testing audio with backend: {self.audio_backend}")
        
        try:
            self.play_gesture_recognized()
            time.sleep(0.5)
            self.play_action_executed()
            print("Audio test completed successfully")
            return True
        except Exception as e:
            print(f"Audio test failed: {e}")
            return False
            
    def get_status(self) -> Dict[str, Any]:
        """Get audio feedback status"""
        return {
            'enabled': self.enabled,
            'volume': self.volume,
            'sound_library': self.sound_library,
            'audio_backend': self.audio_backend,
            'pygame_available': PYGAME_AVAILABLE,
            'winsound_available': WINSOUND_AVAILABLE,
            'sounds_dir': self.sounds_dir
        }


# Import numpy for sound generation
try:
    import numpy as np
except ImportError:
    # Fallback for sound generation without numpy
    import math
    
    class np:
        @staticmethod
        def sin(x):
            if hasattr(x, '__iter__'):
                return [math.sin(val) for val in x]
            return math.sin(x)
            
        @staticmethod
        def pi():
            return math.pi
            
        @staticmethod
        def array(arr):
            return arr

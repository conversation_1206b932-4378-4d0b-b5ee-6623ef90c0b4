"""
GFLOW-26: Enhanced Notification Manager

Provides comprehensive visual feedback including confidence meters,
gesture type indicators, recording feedback, and desktop notifications.
"""

import os
import time
from typing import Dict, Any, Optional, List
from PySide6.QtWidgets import (<PERSON>Widget, QLabel, QProgressBar, QVBoxLayout, 
                               QHBoxLayout, QFrame)
from PySide6.QtCore import QTimer, QPropertyAnimation, QEasingCurve, Qt, Signal
from PySide6.QtGui import QFont, QPalette, QPixmap, QIcon
from config import (ENHANCED_VISUAL_FEEDBACK_CONFIG, DESKTOP_NOTIFICATION_CONFIG, 
                   PROJECT_ROOT)

# Try to import desktop notification library
try:
    from plyer import notification
    PLYER_AVAILABLE = True
except ImportError:
    PLYER_AVAILABLE = False
    print("plyer not available for desktop notifications")


class EnhancedNotificationManager:
    """
    Manages enhanced visual and desktop notifications
    
    Features:
    - Confidence meters
    - Gesture type indicators
    - Recording progress feedback
    - Desktop notifications
    - Performance metrics display
    """
    
    def __init__(self, parent_widget: QWidget = None):
        self.parent_widget = parent_widget
        self.visual_config = ENHANCED_VISUAL_FEEDBACK_CONFIG
        self.desktop_config = DESKTOP_NOTIFICATION_CONFIG
        
        # Visual feedback components
        self.confidence_meter = None
        self.gesture_type_indicator = None
        self.recording_progress = None
        self.performance_display = None
        
        # Desktop notifications
        self.desktop_notifications_enabled = self.desktop_config.get('enabled', False)
        
        # Initialize components
        self._initialize_visual_components()
        
    def _initialize_visual_components(self):
        """Initialize visual feedback components"""
        if not self.parent_widget or not self.visual_config.get('enabled', True):
            return
            
        # Create confidence meter
        if self.visual_config.get('show_confidence_meter', True):
            self.confidence_meter = ConfidenceMeter(self.parent_widget)
            
        # Create gesture type indicator
        if self.visual_config.get('show_gesture_type', True):
            self.gesture_type_indicator = GestureTypeIndicator(self.parent_widget)
            
        # Create recording progress widget
        if self.visual_config.get('recording_progress_bar', True):
            self.recording_progress = RecordingProgressWidget(self.parent_widget)
            
        # Create performance display
        if self.visual_config.get('show_performance_metrics', False):
            self.performance_display = PerformanceDisplay(self.parent_widget)
            
    def show_gesture_recognized(self, gesture_name: str, gesture_type: str, 
                               confidence: float, action_description: str = ""):
        """Show notification for recognized gesture"""
        # Update visual components
        if self.confidence_meter:
            self.confidence_meter.update_confidence(confidence)
            
        if self.gesture_type_indicator:
            self.gesture_type_indicator.show_gesture_type(gesture_type)
            
        # Show desktop notification
        if self.desktop_notifications_enabled and self.desktop_config.get('show_gesture_recognition', True):
            self._show_desktop_notification(
                title="Gesture Recognized",
                message=f"{gesture_name} → {action_description}" if action_description else gesture_name,
                timeout=self.desktop_config.get('timeout', 3000)
            )
            
    def show_action_executed(self, action_description: str, success: bool = True):
        """Show notification for action execution"""
        if self.desktop_notifications_enabled and self.desktop_config.get('show_action_execution', True):
            title = "Action Executed" if success else "Action Failed"
            self._show_desktop_notification(
                title=title,
                message=action_description,
                timeout=self.desktop_config.get('timeout', 3000)
            )
            
    def show_recording_started(self, gesture_type: str = "dynamic"):
        """Show notification for recording start"""
        if self.recording_progress:
            self.recording_progress.start_recording(gesture_type)
            
    def show_recording_stopped(self, success: bool = True, quality_score: float = 0.0):
        """Show notification for recording stop"""
        if self.recording_progress:
            self.recording_progress.stop_recording(success, quality_score)
            
    def update_recording_progress(self, progress: float, quality: float = 0.0):
        """Update recording progress"""
        if self.recording_progress:
            self.recording_progress.update_progress(progress, quality)
            
    def show_training_complete(self, gesture_name: str, accuracy: float):
        """Show notification for training completion"""
        if self.desktop_notifications_enabled and self.desktop_config.get('show_training_complete', True):
            self._show_desktop_notification(
                title="Training Complete",
                message=f"Gesture '{gesture_name}' trained with {accuracy:.1%} accuracy",
                timeout=self.desktop_config.get('timeout', 3000)
            )
            
    def show_error(self, error_message: str):
        """Show error notification"""
        if self.desktop_notifications_enabled and self.desktop_config.get('show_errors', True):
            self._show_desktop_notification(
                title="GestureFlow Error",
                message=error_message,
                timeout=self.desktop_config.get('timeout', 3000)
            )
            
    def update_performance_metrics(self, fps: float, latency: float, cpu_usage: float = 0.0):
        """Update performance metrics display"""
        if self.performance_display:
            self.performance_display.update_metrics(fps, latency, cpu_usage)
            
    def _show_desktop_notification(self, title: str, message: str, timeout: int = 3000):
        """Show desktop notification"""
        if not PLYER_AVAILABLE:
            return
            
        try:
            app_icon = self.desktop_config.get('app_icon', '')
            if app_icon and not os.path.isabs(app_icon):
                app_icon = os.path.join(PROJECT_ROOT, app_icon)
                
            notification.notify(
                title=title,
                message=message,
                app_name=self.desktop_config.get('app_name', 'GestureFlow'),
                app_icon=app_icon if os.path.exists(app_icon) else '',
                timeout=timeout / 1000  # Convert to seconds
            )
        except Exception as e:
            print(f"Error showing desktop notification: {e}")
            
    def set_visual_feedback_enabled(self, enabled: bool):
        """Enable or disable visual feedback"""
        if enabled and not self.confidence_meter:
            self._initialize_visual_components()
        elif not enabled:
            if self.confidence_meter:
                self.confidence_meter.hide()
            if self.gesture_type_indicator:
                self.gesture_type_indicator.hide()
            if self.recording_progress:
                self.recording_progress.hide()
            if self.performance_display:
                self.performance_display.hide()
                
    def set_desktop_notifications_enabled(self, enabled: bool):
        """Enable or disable desktop notifications"""
        self.desktop_notifications_enabled = enabled


class ConfidenceMeter(QWidget):
    """Widget for displaying gesture recognition confidence"""
    
    def __init__(self, parent: QWidget):
        super().__init__(parent)
        self.config = ENHANCED_VISUAL_FEEDBACK_CONFIG
        self.setup_ui()
        self.position_widget()
        
    def setup_ui(self):
        """Setup the confidence meter UI"""
        self.setFixedSize(200, 60)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 0, 0, 180);
                border-radius: 10px;
                color: white;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Confidence")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(title_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        layout.addWidget(self.progress_bar)
        
        self.hide()  # Initially hidden
        
    def position_widget(self):
        """Position the widget based on configuration"""
        position = self.config.get('confidence_meter_position', 'top_right')
        parent_rect = self.parent().rect()
        
        if position == 'top_left':
            self.move(10, 10)
        elif position == 'top_right':
            self.move(parent_rect.width() - self.width() - 10, 10)
        elif position == 'bottom_left':
            self.move(10, parent_rect.height() - self.height() - 10)
        elif position == 'bottom_right':
            self.move(parent_rect.width() - self.width() - 10, 
                     parent_rect.height() - self.height() - 10)
            
    def update_confidence(self, confidence: float):
        """Update confidence display"""
        confidence_percent = int(confidence * 100)
        self.progress_bar.setValue(confidence_percent)
        
        # Update color based on confidence level
        colors = self.config.get('confidence_threshold_colors', {})
        if confidence >= 0.8:
            color = colors.get('high', '#00FF00')
        elif confidence >= 0.5:
            color = colors.get('medium', '#FFFF00')
        else:
            color = colors.get('low', '#FF0000')
            
        self.progress_bar.setStyleSheet(f"""
            QProgressBar::chunk {{
                background-color: {color};
            }}
        """)
        
        self.show()
        
        # Auto-hide after 2 seconds
        QTimer.singleShot(2000, self.hide)


class GestureTypeIndicator(QWidget):
    """Widget for indicating gesture type"""
    
    def __init__(self, parent: QWidget):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the gesture type indicator UI"""
        self.setFixedSize(150, 40)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 0, 0, 180);
                border-radius: 8px;
                color: white;
            }
        """)
        
        layout = QHBoxLayout(self)
        
        # Icon label
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(24, 24)
        layout.addWidget(self.icon_label)
        
        # Type label
        self.type_label = QLabel()
        self.type_label.setFont(QFont("Arial", 9))
        layout.addWidget(self.type_label)
        
        self.hide()
        
    def show_gesture_type(self, gesture_type: str):
        """Show gesture type indicator"""
        type_map = {
            'static': 'Static',
            'dynamic': 'Dynamic',
            'dynamic_predefined': 'Dynamic',
            'custom': 'Custom'
        }
        
        display_text = type_map.get(gesture_type, gesture_type.title())
        self.type_label.setText(display_text)
        
        # Position near confidence meter
        parent_rect = self.parent().rect()
        self.move(parent_rect.width() - self.width() - 10, 80)
        
        self.show()
        QTimer.singleShot(2000, self.hide)


class RecordingProgressWidget(QWidget):
    """Widget for showing recording progress"""
    
    def __init__(self, parent: QWidget):
        super().__init__(parent)
        self.setup_ui()
        self.recording_timer = QTimer()
        self.recording_timer.timeout.connect(self.update_timer)
        self.recording_start_time = 0
        
    def setup_ui(self):
        """Setup recording progress UI"""
        self.setFixedSize(300, 100)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 0, 0, 200);
                border-radius: 15px;
                color: white;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # Title
        self.title_label = QLabel("Recording Gesture")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(self.title_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        layout.addWidget(self.progress_bar)
        
        # Quality indicator
        self.quality_label = QLabel("Quality: --")
        self.quality_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.quality_label)
        
        self.hide()
        
    def start_recording(self, gesture_type: str):
        """Start recording display"""
        self.title_label.setText(f"Recording {gesture_type.title()} Gesture")
        self.progress_bar.setValue(0)
        self.quality_label.setText("Quality: --")
        
        # Center the widget
        parent_rect = self.parent().rect()
        self.move((parent_rect.width() - self.width()) // 2,
                 (parent_rect.height() - self.height()) // 2)
        
        self.recording_start_time = time.time()
        self.recording_timer.start(100)  # Update every 100ms
        self.show()
        
    def stop_recording(self, success: bool, quality_score: float):
        """Stop recording display"""
        self.recording_timer.stop()
        
        if success:
            self.title_label.setText("Recording Complete!")
            self.progress_bar.setValue(100)
            self.quality_label.setText(f"Quality: {quality_score:.1%}")
        else:
            self.title_label.setText("Recording Failed")
            self.quality_label.setText("Please try again")
            
        QTimer.singleShot(2000, self.hide)
        
    def update_progress(self, progress: float, quality: float):
        """Update recording progress"""
        self.progress_bar.setValue(int(progress * 100))
        if quality > 0:
            self.quality_label.setText(f"Quality: {quality:.1%}")
            
    def update_timer(self):
        """Update recording timer"""
        elapsed = time.time() - self.recording_start_time
        max_duration = 5.0  # Assume 5 second max recording
        progress = min(elapsed / max_duration, 1.0)
        self.progress_bar.setValue(int(progress * 100))


class PerformanceDisplay(QWidget):
    """Widget for displaying performance metrics"""
    
    def __init__(self, parent: QWidget):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup performance display UI"""
        self.setFixedSize(200, 80)
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 0, 0, 150);
                border-radius: 10px;
                color: white;
            }
        """)
        
        layout = QVBoxLayout(self)
        
        # FPS label
        self.fps_label = QLabel("FPS: --")
        self.fps_label.setFont(QFont("Arial", 9))
        layout.addWidget(self.fps_label)
        
        # Latency label
        self.latency_label = QLabel("Latency: --")
        self.latency_label.setFont(QFont("Arial", 9))
        layout.addWidget(self.latency_label)
        
        # CPU label
        self.cpu_label = QLabel("CPU: --")
        self.cpu_label.setFont(QFont("Arial", 9))
        layout.addWidget(self.cpu_label)
        
        # Position in bottom left
        self.move(10, self.parent().rect().height() - self.height() - 10)
        
    def update_metrics(self, fps: float, latency: float, cpu_usage: float):
        """Update performance metrics"""
        self.fps_label.setText(f"FPS: {fps:.1f}")
        self.latency_label.setText(f"Latency: {latency:.1f}ms")
        self.cpu_label.setText(f"CPU: {cpu_usage:.1f}%")
        
        self.show()
